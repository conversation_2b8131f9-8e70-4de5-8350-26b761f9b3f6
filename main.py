#!/usr/bin/env python3
"""
狼人杀AI游戏主程序
Phase 1 实现：基础架构演示
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.console_ui import ConsoleUI


def main():
    """主函数"""
    try:
        # 创建控制台UI
        console = ConsoleUI()
        
        # 运行演示
        console.run_demo()
        
    except KeyboardInterrupt:
        print("\n\n游戏被用户中断")
    except Exception as e:
        print(f"\n游戏运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
