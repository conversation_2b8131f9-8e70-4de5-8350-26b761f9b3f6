#!/usr/bin/env python3
"""
Phase 1 功能演示脚本
展示狼人杀AI游戏的核心功能
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase, GameResult, VoteType
from src.models.game_state import GameConfig, GameState
from src.engine.game_engine import GameEngine
from src.players.ai_player import AIPlayer


def print_separator(char='-', length=60):
    """打印分隔线"""
    print(char * length)


def print_header(title):
    """打印标题"""
    print_separator('=')
    print(f" {title} ".center(60, '='))
    print_separator('=')


def role_to_chinese(role):
    """角色转中文"""
    role_map = {
        Role.VILLAGER: "村民",
        Role.WEREWOLF: "狼人", 
        Role.SEER: "预言家",
        Role.WITCH: "女巫",
        Role.HUNTER: "猎人",
        Role.GUARD: "守卫"
    }
    return role_map.get(role, str(role))


def phase_to_chinese(phase):
    """阶段转中文"""
    phase_map = {
        GamePhase.SETUP: "游戏设置",
        GamePhase.NIGHT: "夜晚",
        GamePhase.DAY_DISCUSSION: "白天讨论",
        GamePhase.DAY_VOTING: "白天投票",
        GamePhase.GAME_OVER: "游戏结束"
    }
    return phase_map.get(phase, str(phase))


def demo_game_creation():
    """演示游戏创建"""
    print_header("Phase 1 演示：游戏创建")
    
    # 创建游戏配置
    config = GameConfig(
        total_players=6,
        role_distribution={
            Role.VILLAGER: 2,
            Role.WEREWOLF: 2,
            Role.SEER: 1,
            Role.WITCH: 1
        }
    )
    
    print(f"游戏配置:")
    print(f"  总玩家数: {config.total_players}")
    print(f"  角色分配: {dict((role_to_chinese(k), v) for k, v in config.role_distribution.items())}")
    print(f"  配置有效: {config.validate()}")
    
    # 创建游戏引擎
    engine = GameEngine(config)
    
    # 创建游戏
    player_names = ["Alice", "Bob", "Charlie", "David", "Eve", "Frank"]
    game_state = engine.create_game("demo_game", player_names)
    
    print(f"\n游戏创建成功!")
    print(f"游戏ID: {game_state.game_id}")
    print(f"当前阶段: {phase_to_chinese(game_state.current_phase)}")
    
    print(f"\n玩家角色分配:")
    for player_id, player in game_state.players.items():
        print(f"  {player_id}. {player.name} - {role_to_chinese(player.role)}")
    
    return engine, game_state


def demo_ai_players(game_state):
    """演示AI玩家"""
    print_header("Phase 1 演示：AI玩家系统")
    
    # 创建AI玩家实例
    ai_players = {}
    for player_id, player_info in game_state.players.items():
        ai_player = AIPlayer(player_id, player_info.name)
        ai_player.set_role(player_info.role)
        ai_players[player_id] = ai_player
    
    print("AI玩家个性特征:")
    for player_id, ai_player in ai_players.items():
        print(f"\n{ai_player.name} ({role_to_chinese(ai_player.role)}):")
        traits = ai_player.personality_traits
        print(f"  攻击性: {traits['aggressiveness']:.2f}")
        print(f"  怀疑度: {traits['suspicion']:.2f}")
        print(f"  忠诚度: {traits['loyalty']:.2f}")
        print(f"  分析能力: {traits['analysis']:.2f}")
    
    # 演示AI决策
    print(f"\n演示AI投票决策:")
    game_state.current_phase = GamePhase.DAY_VOTING
    
    for player_id, ai_player in ai_players.items():
        if game_state.players[player_id].is_alive():
            target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
            target_name = game_state.players[target].name if target else "弃权"
            print(f"  {ai_player.name} 投票给: {target_name}")
    
    # 演示AI发言
    print(f"\n演示AI发言:")
    for player_id, ai_player in ai_players.items():
        if game_state.players[player_id].is_alive():
            speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
            print(f"  {ai_player.name}: {speech}")
    
    return ai_players


def demo_game_flow(engine, game_state, ai_players):
    """演示游戏流程"""
    print_header("Phase 1 演示：游戏流程")
    
    # 开始游戏
    engine.start_game()
    print(f"游戏开始! 当前阶段: {phase_to_chinese(game_state.current_phase)}")
    print(f"回合数: {game_state.current_round}")
    
    # 模拟几个回合
    round_count = 0
    max_rounds = 3
    
    while not engine.is_game_over() and round_count < max_rounds:
        round_count += 1
        print(f"\n--- 第 {round_count} 轮演示 ---")
        print(f"当前阶段: {phase_to_chinese(game_state.current_phase)}")
        
        if game_state.current_phase == GamePhase.NIGHT:
            print("夜晚阶段 - AI玩家进行夜晚行动...")
            
            # 狼人投票杀人
            werewolves = game_state.get_werewolves()
            if werewolves:
                werewolf = werewolves[0]
                ai_werewolf = ai_players[werewolf.player_id]
                target = ai_werewolf.make_vote_decision(game_state, VoteType.WEREWOLF_KILL)
                if target:
                    engine.process_vote(werewolf.player_id, target, VoteType.WEREWOLF_KILL)
                    print(f"  狼人 {werewolf.name} 选择杀死 {game_state.players[target].name}")
            
            # 预言家查验
            seers = game_state.get_players_by_role(Role.SEER)
            if seers:
                seer = seers[0]
                ai_seer = ai_players[seer.player_id]
                target = ai_seer.make_special_action_decision(game_state, "seer_check")
                if target:
                    engine.process_special_action(seer.player_id, "seer_check", target)
                    print(f"  预言家 {seer.name} 查验 {game_state.players[target].name}")
            
            # 处理夜晚结果
            killed_player_id = engine.process_werewolf_kill()
            if killed_player_id:
                killed_player = game_state.players[killed_player_id]
                print(f"  {killed_player.name} 被狼人杀死了!")
        
        elif game_state.current_phase == GamePhase.DAY_DISCUSSION:
            print("白天讨论阶段 - AI玩家发言...")
            alive_players = game_state.get_alive_players()
            for player in alive_players[:3]:  # 只显示前3个玩家的发言
                ai_player = ai_players[player.player_id]
                speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
                print(f"  {player.name}: {speech}")
        
        elif game_state.current_phase == GamePhase.DAY_VOTING:
            print("白天投票阶段 - AI玩家投票...")
            alive_players = game_state.get_alive_players()
            for player in alive_players:
                ai_player = ai_players[player.player_id]
                target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
                if target:
                    engine.process_vote(player.player_id, target, VoteType.ELIMINATION)
                    print(f"  {player.name} 投票给 {game_state.players[target].name}")
            
            # 显示投票结果
            if game_state.vote_counts:
                print("  投票统计:")
                for target_id, votes in game_state.vote_counts.items():
                    target_name = game_state.players[target_id].name
                    print(f"    {target_name}: {votes}票")
        
        # 推进到下一阶段
        engine.advance_phase()
        
        # 检查游戏结果
        result = game_state.check_game_end()
        if result != GameResult.ONGOING:
            print(f"\n游戏结束! 结果: {result}")
            break
        
        time.sleep(1)  # 暂停一秒便于观察
    
    # 显示最终状态
    print(f"\n最终游戏状态:")
    print(f"  阶段: {phase_to_chinese(game_state.current_phase)}")
    print(f"  回合数: {game_state.current_round}")
    print(f"  结果: {game_state.game_result}")
    
    print(f"\n存活玩家:")
    for player in game_state.get_alive_players():
        print(f"  {player.name} ({role_to_chinese(player.role)})")


def main():
    """主演示函数"""
    print("狼人杀AI游戏 - Phase 1 功能演示")
    print("=" * 60)
    
    try:
        # 演示游戏创建
        engine, game_state = demo_game_creation()
        input("\n按回车键继续...")
        
        # 演示AI玩家
        ai_players = demo_ai_players(game_state)
        input("\n按回车键继续...")
        
        # 演示游戏流程
        demo_game_flow(engine, game_state, ai_players)
        
        print("\n" + "=" * 60)
        print("Phase 1 演示完成!")
        print("已实现的核心功能:")
        print("✅ 游戏状态数据结构")
        print("✅ 基础游戏引擎")
        print("✅ 角色系统和枚举")
        print("✅ AI玩家基类")
        print("✅ 控制台界面")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n演示出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
