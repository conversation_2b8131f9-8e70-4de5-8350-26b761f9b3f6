# 狼人杀AI游戏

一个基于Python的狼人杀AI游戏实现，支持多种AI策略和人机对战。

## Phase 1: 基础架构 ✅

Phase 1 已完成，包含以下核心功能：

### 已实现功能

1. **游戏状态数据结构**
   - 玩家信息和状态管理
   - 游戏配置和回合信息
   - 投票和行动记录系统

2. **基础游戏引擎**
   - 回合管理和状态转换
   - 事件驱动架构
   - 投票和特殊行动处理

3. **角色系统**
   - 完整的角色枚举（村民、狼人、预言家、女巫、守卫、猎人等）
   - 阵营划分和胜负条件
   - 角色特殊能力框架

4. **AI玩家系统**
   - 抽象玩家基类
   - AI玩家基础实现
   - 个性化特征和策略框架

5. **控制台界面**
   - 简单的文本界面
   - 游戏状态显示
   - 基础交互功能

## 项目结构

```
wolfkill/
├── src/
│   ├── models/           # 数据模型
│   │   ├── enums.py     # 游戏枚举定义
│   │   ├── player.py    # 玩家相关数据结构
│   │   └── game_state.py # 游戏状态管理
│   ├── engine/          # 游戏引擎
│   │   └── game_engine.py # 核心游戏逻辑
│   ├── players/         # 玩家实现
│   │   ├── base_player.py # 玩家抽象基类
│   │   └── ai_player.py   # AI玩家实现
│   └── ui/              # 用户界面
│       └── console_ui.py  # 控制台界面
├── main.py              # 主程序入口
├── test_basic.py        # 基础功能测试
├── todo.md              # 开发计划
└── README.md            # 项目说明
```

## 快速开始

### 运行游戏

```bash
python main.py
```

### 运行测试

```bash
python test_basic.py
```

## 游戏特性

### 支持的角色

- **村民**: 普通村民，参与讨论和投票
- **狼人**: 夜晚杀人，白天伪装
- **预言家**: 每晚查验一个玩家身份
- **女巫**: 拥有解药和毒药各一瓶
- **守卫**: 每晚保护一个玩家
- **猎人**: 死亡时可以开枪带走一人

### AI特性

- **个性化特征**: 每个AI都有独特的性格特征
- **动态策略**: 基于游戏状态和历史信息做决策
- **知识管理**: 维护对其他玩家的认知和信任度
- **事件响应**: 根据游戏事件调整策略

### 游戏流程

1. **游戏设置**: 配置玩家数量和角色分配
2. **夜晚阶段**: 狼人杀人，特殊角色使用技能
3. **白天讨论**: 所有玩家发言讨论
4. **投票阶段**: 投票淘汰可疑玩家
5. **胜负判定**: 检查是否达成胜利条件

## 技术架构

### 设计模式

- **事件驱动**: 游戏引擎使用事件系统解耦组件
- **策略模式**: AI玩家使用可插拔的策略系统
- **状态机**: 游戏阶段转换使用状态机模式

### 核心组件

1. **GameEngine**: 游戏核心逻辑和流程控制
2. **GameState**: 游戏状态管理和数据持久化
3. **BasePlayer**: 玩家抽象接口定义
4. **AIPlayer**: AI玩家策略实现

## 开发计划

- [x] **Phase 1**: 基础架构 (已完成)
- [ ] **Phase 2**: 核心游戏逻辑
- [ ] **Phase 3**: AI策略开发
- [ ] **Phase 4**: 高级功能
- [ ] **Phase 5**: 用户体验
- [ ] **Phase 6**: 测试和优化

详细计划请查看 [todo.md](todo.md)

## 示例用法

### 创建游戏

```python
from src.models.game_state import GameConfig
from src.models.enums import Role
from src.engine.game_engine import GameEngine

# 创建游戏配置
config = GameConfig(
    total_players=6,
    role_distribution={
        Role.VILLAGER: 2,
        Role.WEREWOLF: 2,
        Role.SEER: 1,
        Role.WITCH: 1
    }
)

# 创建游戏引擎
engine = GameEngine(config)

# 创建游戏
player_names = ["Alice", "Bob", "Charlie", "David", "Eve", "Frank"]
game_state = engine.create_game("my_game", player_names)

# 开始游戏
engine.start_game()
```

### 创建AI玩家

```python
from src.players.ai_player import AIPlayer
from src.models.enums import Role

# 创建AI玩家
ai_player = AIPlayer(1, "智能玩家1", difficulty="hard")
ai_player.set_role(Role.VILLAGER)

# AI做决策
vote_target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License
