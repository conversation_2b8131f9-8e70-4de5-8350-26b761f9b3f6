"""
玩家抽象基类
定义所有玩家类型的通用接口
"""
from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any

from ..models.enums import GamePhase, Role, VoteType
from ..models.game_state import GameState
from ..models.player import PlayerInfo, PlayerKnowledge


class BasePlayer(ABC):
    """玩家抽象基类"""
    
    def __init__(self, player_id: int, name: str):
        """初始化玩家"""
        self.player_id = player_id
        self.name = name
        self.role: Optional[Role] = None
        self.knowledge = PlayerKnowledge(player_id=player_id)
        self.is_active = True
    
    def set_role(self, role: Role):
        """设置玩家角色"""
        self.role = role
    
    def get_role(self) -> Optional[Role]:
        """获取玩家角色"""
        return self.role
    
    @abstractmethod
    def make_vote_decision(self, game_state: GameState, vote_type: VoteType) -> Optional[int]:
        """做出投票决策
        
        Args:
            game_state: 当前游戏状态
            vote_type: 投票类型
            
        Returns:
            目标玩家ID，None表示弃权
        """
        pass
    
    @abstractmethod
    def make_special_action_decision(self, game_state: GameState, action_type: str) -> Optional[int]:
        """做出特殊行动决策
        
        Args:
            game_state: 当前游戏状态
            action_type: 行动类型
            
        Returns:
            目标玩家ID，None表示不行动
        """
        pass
    
    @abstractmethod
    def generate_speech(self, game_state: GameState, phase: GamePhase) -> str:
        """生成发言内容
        
        Args:
            game_state: 当前游戏状态
            phase: 当前游戏阶段
            
        Returns:
            发言内容
        """
        pass
    
    @abstractmethod
    def process_game_event(self, event_type: str, event_data: Dict[str, Any]):
        """处理游戏事件
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
        """
        pass
    
    def update_knowledge(self, game_state: GameState):
        """更新玩家知识状态"""
        # 基础知识更新：观察公开信息
        for player_id, player_info in game_state.players.items():
            if player_id != self.player_id:
                # 更新存活状态
                if not player_info.is_alive():
                    self.knowledge.trust_levels[player_id] = 0.0
    
    def get_alive_players(self, game_state: GameState) -> List[int]:
        """获取存活玩家ID列表"""
        return [pid for pid, player in game_state.players.items() 
                if player.is_alive() and pid != self.player_id]
    
    def get_trust_level(self, target_id: int) -> float:
        """获取对目标玩家的信任度"""
        return self.knowledge.trust_levels.get(target_id, 0.5)
    
    def update_trust_level(self, target_id: int, trust_change: float):
        """更新对目标玩家的信任度"""
        current_trust = self.knowledge.trust_levels.get(target_id, 0.5)
        new_trust = max(0.0, min(1.0, current_trust + trust_change))
        self.knowledge.trust_levels[target_id] = new_trust


class HumanPlayer(BasePlayer):
    """人类玩家类"""
    
    def __init__(self, player_id: int, name: str):
        super().__init__(player_id, name)
        self.pending_decisions = {}
    
    def make_vote_decision(self, game_state: GameState, vote_type: VoteType) -> Optional[int]:
        """人类玩家投票决策（需要外部输入）"""
        # 这里应该通过UI获取人类玩家的输入
        # 暂时返回None，表示需要等待输入
        return self.pending_decisions.get(f"vote_{vote_type.name}")
    
    def make_special_action_decision(self, game_state: GameState, action_type: str) -> Optional[int]:
        """人类玩家特殊行动决策（需要外部输入）"""
        return self.pending_decisions.get(f"action_{action_type}")
    
    def generate_speech(self, game_state: GameState, phase: GamePhase) -> str:
        """人类玩家发言（需要外部输入）"""
        return self.pending_decisions.get(f"speech_{phase.name}", "")
    
    def process_game_event(self, event_type: str, event_data: Dict[str, Any]):
        """处理游戏事件"""
        # 人类玩家主要通过UI观察游戏事件
        pass
    
    def set_decision(self, decision_type: str, value: Any):
        """设置人类玩家的决策"""
        self.pending_decisions[decision_type] = value
    
    def clear_decisions(self):
        """清空待决策"""
        self.pending_decisions.clear()
