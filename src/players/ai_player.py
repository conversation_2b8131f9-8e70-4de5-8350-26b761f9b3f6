"""
AI玩家基类
提供AI玩家的基础实现和通用策略
"""
import random
from typing import Optional, List, Dict, Any

from ..models.enums import GamePhase, Role, VoteType, Faction
from ..models.game_state import GameState
from .base_player import BasePlayer


class AIPlayer(BasePlayer):
    """AI玩家基类"""
    
    def __init__(self, player_id: int, name: str, difficulty: str = "normal"):
        super().__init__(player_id, name)
        self.difficulty = difficulty
        self.personality_traits = self._generate_personality()
        self.speech_patterns = []
        self.voting_history = []
    
    def _generate_personality(self) -> Dict[str, float]:
        """生成AI个性特征"""
        return {
            "aggressiveness": random.uniform(0.3, 0.8),    # 攻击性
            "suspicion": random.uniform(0.2, 0.7),         # 怀疑度
            "loyalty": random.uniform(0.4, 0.9),           # 忠诚度
            "deception": random.uniform(0.1, 0.6),         # 欺骗能力
            "analysis": random.uniform(0.3, 0.8),          # 分析能力
        }
    
    def make_vote_decision(self, game_state: GameState, vote_type: VoteType) -> Optional[int]:
        """AI投票决策"""
        if vote_type == VoteType.ELIMINATION:
            return self._make_elimination_vote(game_state)
        elif vote_type == VoteType.WEREWOLF_KILL:
            return self._make_werewolf_kill_vote(game_state)
        return None
    
    def _make_elimination_vote(self, game_state: GameState) -> Optional[int]:
        """白天淘汰投票决策"""
        alive_players = self.get_alive_players(game_state)
        if not alive_players:
            return None
        
        # 基础策略：投票给信任度最低的玩家
        candidates = []
        for player_id in alive_players:
            trust = self.get_trust_level(player_id)
            suspicion = 1.0 - trust
            
            # 添加随机因素
            suspicion += random.uniform(-0.1, 0.1)
            candidates.append((player_id, suspicion))
        
        # 按怀疑度排序，选择最可疑的
        candidates.sort(key=lambda x: x[1], reverse=True)
        
        # 根据个性特征调整决策
        if self.personality_traits["analysis"] > 0.6:
            # 高分析能力：更仔细考虑
            return self._analytical_vote_decision(game_state, candidates)
        else:
            # 普通决策：选择最可疑的
            return candidates[0][0] if candidates else None
    
    def _make_werewolf_kill_vote(self, game_state: GameState) -> Optional[int]:
        """狼人杀人投票决策"""
        if not self.role or self.role != Role.WEREWOLF:
            return None
        
        alive_players = self.get_alive_players(game_state)
        if not alive_players:
            return None
        
        # 狼人策略：优先杀死威胁最大的村民
        candidates = []
        for player_id in alive_players:
            player = game_state.players[player_id]
            threat_level = self._calculate_threat_level(player, game_state)
            candidates.append((player_id, threat_level))
        
        # 按威胁度排序
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[0][0] if candidates else None
    
    def _analytical_vote_decision(self, game_state: GameState, candidates: List[tuple]) -> Optional[int]:
        """分析型投票决策"""
        # 更复杂的分析逻辑
        # 考虑投票历史、发言内容等
        if len(candidates) > 1:
            # 避免总是投票给同一个人
            top_candidates = candidates[:min(3, len(candidates))]
            weights = [3, 2, 1][:len(top_candidates)]
            
            selected = random.choices(top_candidates, weights=weights)[0]
            return selected[0]
        
        return candidates[0][0] if candidates else None
    
    def _calculate_threat_level(self, player: 'PlayerInfo', game_state: GameState) -> float:
        """计算玩家威胁等级"""
        threat = 0.0
        
        # 根据角色计算威胁
        if player.role == Role.SEER:
            threat += 0.8  # 预言家威胁很大
        elif player.role == Role.WITCH:
            threat += 0.6  # 女巫威胁较大
        elif player.role == Role.HUNTER:
            threat += 0.4  # 猎人有一定威胁
        elif player.role == Role.GUARD:
            threat += 0.5  # 守卫威胁中等
        
        # 根据信任度调整
        trust = self.get_trust_level(player.player_id)
        if trust < 0.3:  # 如果这个人很可疑，威胁度降低
            threat *= 0.5
        
        return threat
    
    def make_special_action_decision(self, game_state: GameState, action_type: str) -> Optional[int]:
        """特殊行动决策"""
        if action_type == "seer_check":
            return self._make_seer_check_decision(game_state)
        elif action_type == "guard_protect":
            return self._make_guard_protect_decision(game_state)
        elif action_type == "witch_save":
            return self._make_witch_save_decision(game_state)
        elif action_type == "witch_poison":
            return self._make_witch_poison_decision(game_state)
        
        return None
    
    def _make_seer_check_decision(self, game_state: GameState) -> Optional[int]:
        """预言家查验决策"""
        alive_players = self.get_alive_players(game_state)
        if not alive_players:
            return None
        
        # 优先查验最可疑的玩家
        candidates = []
        for player_id in alive_players:
            # 避免查验已知角色的玩家
            if player_id in self.knowledge.known_roles:
                continue
            
            suspicion = 1.0 - self.get_trust_level(player_id)
            candidates.append((player_id, suspicion))
        
        if candidates:
            candidates.sort(key=lambda x: x[1], reverse=True)
            return candidates[0][0]
        
        # 如果没有特别可疑的，随机选择
        return random.choice(alive_players) if alive_players else None
    
    def _make_guard_protect_decision(self, game_state: GameState) -> Optional[int]:
        """守卫保护决策"""
        alive_players = self.get_alive_players(game_state)
        if not alive_players:
            return None
        
        # 优先保护重要角色
        candidates = []
        for player_id in alive_players:
            protection_value = self._calculate_protection_value(player_id, game_state)
            candidates.append((player_id, protection_value))
        
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[0][0] if candidates else None
    
    def _calculate_protection_value(self, player_id: int, game_state: GameState) -> float:
        """计算保护价值"""
        value = 0.5  # 基础价值
        
        # 根据已知角色调整
        if player_id in self.knowledge.known_roles:
            role = self.knowledge.known_roles[player_id]
            if role == Role.SEER:
                value += 0.4
            elif role == Role.WITCH:
                value += 0.3
        
        # 根据信任度调整
        trust = self.get_trust_level(player_id)
        value += trust * 0.3
        
        return value
    
    def _make_witch_save_decision(self, game_state: GameState) -> Optional[int]:
        """女巫救人决策"""
        # 简化实现：需要知道谁被杀
        # 这里需要更复杂的逻辑来确定救人目标
        return None
    
    def _make_witch_poison_decision(self, game_state: GameState) -> Optional[int]:
        """女巫毒人决策"""
        alive_players = self.get_alive_players(game_state)
        if not alive_players:
            return None
        
        # 毒死最可疑的玩家
        most_suspicious = None
        max_suspicion = 0.7  # 只有足够可疑才使用毒药
        
        for player_id in alive_players:
            suspicion = 1.0 - self.get_trust_level(player_id)
            if suspicion > max_suspicion:
                max_suspicion = suspicion
                most_suspicious = player_id
        
        return most_suspicious
    
    def generate_speech(self, game_state: GameState, phase: GamePhase) -> str:
        """生成发言"""
        if phase == GamePhase.DAY_DISCUSSION:
            return self._generate_day_speech(game_state)
        elif phase == GamePhase.DAY_VOTING:
            return self._generate_voting_speech(game_state)
        
        return ""
    
    def _generate_day_speech(self, game_state: GameState) -> str:
        """生成白天讨论发言"""
        speeches = [
            "我觉得需要仔细分析一下昨晚的情况。",
            "从投票情况来看，有些人的行为很可疑。",
            "我们需要找出狼人，不能让他们继续隐藏。",
            "大家要冷静分析，不要被情绪影响判断。"
        ]
        
        # 根据个性特征选择发言风格
        if self.personality_traits["aggressiveness"] > 0.6:
            speeches.extend([
                "我强烈怀疑某些人的身份！",
                "必须要有人站出来承担责任！"
            ])
        
        return random.choice(speeches)
    
    def _generate_voting_speech(self, game_state: GameState) -> str:
        """生成投票阶段发言"""
        speeches = [
            "我的票投给最可疑的人。",
            "基于目前的信息，我做出这个选择。",
            "希望大家都能理性投票。"
        ]
        
        return random.choice(speeches)
    
    def process_game_event(self, event_type: str, event_data: Dict[str, Any]):
        """处理游戏事件"""
        if event_type == "player_eliminated":
            self._process_elimination_event(event_data)
        elif event_type == "player_killed":
            self._process_kill_event(event_data)
        elif event_type == "seer_check_result":
            self._process_seer_result(event_data)
        elif event_type == "vote_cast":
            self._process_vote_event(event_data)
    
    def _process_elimination_event(self, event_data: Dict[str, Any]):
        """处理淘汰事件"""
        eliminated_id = event_data.get("player_id")
        if eliminated_id:
            # 更新知识：被淘汰的玩家信任度归零
            self.knowledge.trust_levels[eliminated_id] = 0.0
    
    def _process_kill_event(self, event_data: Dict[str, Any]):
        """处理杀死事件"""
        killed_id = event_data.get("player_id")
        if killed_id:
            # 被杀死的玩家通常是好人
            self.knowledge.trust_levels[killed_id] = 1.0
    
    def _process_seer_result(self, event_data: Dict[str, Any]):
        """处理预言家查验结果"""
        if event_data.get("seer_id") == self.player_id:
            target_id = event_data.get("target_id")
            role = event_data.get("role")
            if target_id and role:
                self.knowledge.update_role_knowledge(target_id, role, 1.0)
    
    def _process_vote_event(self, event_data: Dict[str, Any]):
        """处理投票事件"""
        voter_id = event_data.get("voter_id")
        target_id = event_data.get("target_id")
        
        if voter_id and target_id:
            # 记录投票历史用于分析
            self.voting_history.append((voter_id, target_id))
            
            # 简单的信任度调整
            if voter_id != self.player_id:
                # 如果某人投票给我信任的人，降低对他的信任
                if self.get_trust_level(target_id) > 0.6:
                    self.update_trust_level(voter_id, -0.1)
