"""
胜负条件判定系统
实现精确的胜负条件判定，包括特殊情况处理
"""
from typing import Dict, List, Optional, Tuple
from ..models.enums import GameResult, Role, Faction
from ..models.game_state import GameState


class VictoryConditionChecker:
    """胜负条件检查器"""
    
    def __init__(self, game_state: GameState):
        self.game_state = game_state
    
    def check_victory_conditions(self) -> Dict[str, any]:
        """检查胜负条件"""
        alive_players = self.game_state.get_alive_players()
        
        if not alive_players:
            return {
                "result": GameResult.VILLAGERS_WIN,
                "reason": "all_dead",
                "message": "所有玩家死亡，村民阵营获胜（默认）",
                "details": {}
            }
        
        # 统计存活玩家
        faction_counts = self._count_alive_by_faction()
        role_counts = self._count_alive_by_role()
        
        # 检查狼人胜利条件
        werewolf_victory = self._check_werewolf_victory(faction_counts, role_counts)
        if werewolf_victory:
            return werewolf_victory
        
        # 检查村民胜利条件
        villager_victory = self._check_villager_victory(faction_counts, role_counts)
        if villager_victory:
            return villager_victory
        
        # 检查特殊胜利条件
        special_victory = self._check_special_victory_conditions(faction_counts, role_counts)
        if special_victory:
            return special_victory
        
        # 游戏继续
        return {
            "result": GameResult.ONGOING,
            "reason": "game_continues",
            "message": "游戏继续进行",
            "details": {
                "faction_counts": faction_counts,
                "role_counts": role_counts
            }
        }
    
    def _count_alive_by_faction(self) -> Dict[Faction, int]:
        """统计各阵营存活人数"""
        counts = {
            Faction.VILLAGERS: 0,
            Faction.WEREWOLVES: 0,
            Faction.NEUTRAL: 0
        }
        
        for player in self.game_state.get_alive_players():
            counts[player.faction] += 1
        
        return counts
    
    def _count_alive_by_role(self) -> Dict[Role, int]:
        """统计各角色存活人数"""
        counts = {}
        
        for player in self.game_state.get_alive_players():
            counts[player.role] = counts.get(player.role, 0) + 1
        
        return counts
    
    def _check_werewolf_victory(self, faction_counts: Dict[Faction, int], 
                               role_counts: Dict[Role, int]) -> Optional[Dict[str, any]]:
        """检查狼人胜利条件"""
        werewolf_count = faction_counts[Faction.WEREWOLVES]
        villager_count = faction_counts[Faction.VILLAGERS]
        
        # 基本胜利条件：狼人数量 >= 村民数量
        if werewolf_count >= villager_count and werewolf_count > 0:
            return {
                "result": GameResult.WEREWOLVES_WIN,
                "reason": "werewolf_majority",
                "message": f"狼人获胜！狼人 {werewolf_count} 人，村民 {villager_count} 人",
                "details": {
                    "werewolf_count": werewolf_count,
                    "villager_count": villager_count,
                    "surviving_werewolves": [
                        {"id": p.player_id, "name": p.name} 
                        for p in self.game_state.get_alive_players() 
                        if p.faction == Faction.WEREWOLVES
                    ]
                }
            }
        
        # 特殊情况：所有村民死亡
        if villager_count == 0 and werewolf_count > 0:
            return {
                "result": GameResult.WEREWOLVES_WIN,
                "reason": "all_villagers_dead",
                "message": "狼人获胜！所有村民已死亡",
                "details": {
                    "werewolf_count": werewolf_count,
                    "villager_count": 0
                }
            }
        
        return None
    
    def _check_villager_victory(self, faction_counts: Dict[Faction, int], 
                               role_counts: Dict[Role, int]) -> Optional[Dict[str, any]]:
        """检查村民胜利条件"""
        werewolf_count = faction_counts[Faction.WEREWOLVES]
        villager_count = faction_counts[Faction.VILLAGERS]
        
        # 基本胜利条件：所有狼人死亡
        if werewolf_count == 0 and villager_count > 0:
            return {
                "result": GameResult.VILLAGERS_WIN,
                "reason": "all_werewolves_dead",
                "message": "村民获胜！所有狼人已被淘汰",
                "details": {
                    "werewolf_count": 0,
                    "villager_count": villager_count,
                    "surviving_villagers": [
                        {"id": p.player_id, "name": p.name, "role": p.role.name} 
                        for p in self.game_state.get_alive_players() 
                        if p.faction == Faction.VILLAGERS
                    ]
                }
            }
        
        return None
    
    def _check_special_victory_conditions(self, faction_counts: Dict[Faction, int], 
                                        role_counts: Dict[Role, int]) -> Optional[Dict[str, any]]:
        """检查特殊胜利条件"""
        
        # 检查是否只剩下特殊角色的特殊情况
        total_alive = sum(faction_counts.values())
        
        # 如果只剩下1-2人，可能需要特殊处理
        if total_alive <= 2:
            return self._handle_endgame_scenario(faction_counts, role_counts)
        
        # 检查是否存在无法继续游戏的情况
        if self._is_game_deadlocked(faction_counts, role_counts):
            return {
                "result": GameResult.VILLAGERS_WIN,
                "reason": "game_deadlock",
                "message": "游戏陷入僵局，村民获胜",
                "details": {
                    "faction_counts": faction_counts,
                    "role_counts": role_counts
                }
            }
        
        return None
    
    def _handle_endgame_scenario(self, faction_counts: Dict[Faction, int], 
                                role_counts: Dict[Role, int]) -> Optional[Dict[str, any]]:
        """处理终局场景"""
        total_alive = sum(faction_counts.values())
        werewolf_count = faction_counts[Faction.WEREWOLVES]
        villager_count = faction_counts[Faction.VILLAGERS]
        
        if total_alive == 1:
            # 只剩一人
            survivor = self.game_state.get_alive_players()[0]
            
            if survivor.faction == Faction.WEREWOLVES:
                return {
                    "result": GameResult.WEREWOLVES_WIN,
                    "reason": "last_survivor_werewolf",
                    "message": f"狼人 {survivor.name} 是最后的幸存者",
                    "details": {"survivor": {"id": survivor.player_id, "name": survivor.name}}
                }
            else:
                return {
                    "result": GameResult.VILLAGERS_WIN,
                    "reason": "last_survivor_villager",
                    "message": f"村民 {survivor.name} 是最后的幸存者",
                    "details": {"survivor": {"id": survivor.player_id, "name": survivor.name}}
                }
        
        elif total_alive == 2:
            # 剩两人的情况
            if werewolf_count == 1 and villager_count == 1:
                # 一狼一民：狼人获胜
                return {
                    "result": GameResult.WEREWOLVES_WIN,
                    "reason": "one_vs_one_werewolf_wins",
                    "message": "一对一对决，狼人获胜",
                    "details": {
                        "survivors": [
                            {"id": p.player_id, "name": p.name, "role": p.role.name} 
                            for p in self.game_state.get_alive_players()
                        ]
                    }
                }
            elif werewolf_count == 2:
                # 两狼：狼人获胜
                return {
                    "result": GameResult.WEREWOLVES_WIN,
                    "reason": "two_werewolves_remain",
                    "message": "两名狼人幸存，狼人获胜",
                    "details": {"werewolf_count": 2}
                }
            elif villager_count == 2:
                # 两村民：村民获胜
                return {
                    "result": GameResult.VILLAGERS_WIN,
                    "reason": "two_villagers_remain",
                    "message": "两名村民幸存，村民获胜",
                    "details": {"villager_count": 2}
                }
        
        return None
    
    def _is_game_deadlocked(self, faction_counts: Dict[Faction, int], 
                           role_counts: Dict[Role, int]) -> bool:
        """检查游戏是否陷入僵局"""
        
        # 检查是否存在无法打破的平衡
        werewolf_count = faction_counts[Faction.WEREWOLVES]
        villager_count = faction_counts[Faction.VILLAGERS]
        
        # 如果狼人和村民数量相等且都大于1，可能陷入僵局
        if werewolf_count == villager_count and werewolf_count > 1:
            # 检查是否有特殊角色能打破僵局
            has_seer = role_counts.get(Role.SEER, 0) > 0
            has_witch = role_counts.get(Role.WITCH, 0) > 0
            has_guard = role_counts.get(Role.GUARD, 0) > 0
            
            # 如果没有特殊角色，可能陷入僵局
            if not (has_seer or has_witch or has_guard):
                return True
        
        return False
    
    def get_victory_probability(self) -> Dict[str, float]:
        """计算各阵营胜利概率（用于AI决策）"""
        faction_counts = self._count_alive_by_faction()
        role_counts = self._count_alive_by_role()
        
        werewolf_count = faction_counts[Faction.WEREWOLVES]
        villager_count = faction_counts[Faction.VILLAGERS]
        total_alive = werewolf_count + villager_count
        
        if total_alive == 0:
            return {"villagers": 1.0, "werewolves": 0.0}
        
        # 基础概率计算
        if werewolf_count == 0:
            return {"villagers": 1.0, "werewolves": 0.0}
        
        if villager_count == 0:
            return {"villagers": 0.0, "werewolves": 1.0}
        
        # 简单的概率模型
        werewolf_advantage = werewolf_count / total_alive
        
        # 考虑特殊角色的影响
        special_role_bonus = 0.0
        if role_counts.get(Role.SEER, 0) > 0:
            special_role_bonus += 0.1
        if role_counts.get(Role.WITCH, 0) > 0:
            special_role_bonus += 0.05
        if role_counts.get(Role.GUARD, 0) > 0:
            special_role_bonus += 0.05
        
        # 调整概率
        werewolf_prob = max(0.1, min(0.9, werewolf_advantage - special_role_bonus))
        villager_prob = 1.0 - werewolf_prob
        
        return {
            "villagers": villager_prob,
            "werewolves": werewolf_prob
        }
    
    def get_game_balance_info(self) -> Dict[str, any]:
        """获取游戏平衡信息"""
        faction_counts = self._count_alive_by_faction()
        role_counts = self._count_alive_by_role()
        victory_prob = self.get_victory_probability()
        
        return {
            "faction_counts": faction_counts,
            "role_counts": role_counts,
            "victory_probability": victory_prob,
            "game_phase": self.game_state.current_phase,
            "round_number": self.game_state.current_round,
            "balance_score": abs(victory_prob["villagers"] - victory_prob["werewolves"]),
            "is_balanced": abs(victory_prob["villagers"] - victory_prob["werewolves"]) < 0.3
        }
    
    def predict_game_length(self) -> Dict[str, any]:
        """预测游戏剩余长度"""
        faction_counts = self._count_alive_by_faction()
        total_alive = sum(faction_counts.values())
        
        # 简单的预测模型
        if total_alive <= 3:
            estimated_rounds = 1
        elif total_alive <= 5:
            estimated_rounds = 2
        else:
            estimated_rounds = max(1, (total_alive - 3) // 2)
        
        return {
            "estimated_rounds_remaining": estimated_rounds,
            "total_players_alive": total_alive,
            "current_round": self.game_state.current_round,
            "estimated_total_rounds": self.game_state.current_round + estimated_rounds
        }
