"""
白天阶段逻辑处理
实现详细的讨论、投票和处决逻辑
"""
import random
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta

from ..models.enums import GamePhase, Role, PlayerStatus, VoteType
from ..models.game_state import GameState
from ..models.player import PlayerAction, PlayerVote


class DayPhaseManager:
    """白天阶段管理器"""
    
    def __init__(self, game_state: GameState):
        self.game_state = game_state
        self.discussion_speeches = []  # 讨论阶段发言记录
        self.voting_deadline = None
        self.discussion_deadline = None
    
    def start_discussion_phase(self) -> Dict[str, any]:
        """开始讨论阶段"""
        self.game_state.current_phase = GamePhase.DAY_DISCUSSION
        
        # 设置讨论时间限制
        if self.game_state.config.discussion_time_limit > 0:
            self.discussion_deadline = datetime.now() + timedelta(
                seconds=self.game_state.config.discussion_time_limit
            )
        
        # 清空之前的发言记录
        self.discussion_speeches.clear()
        
        # 公布夜晚结果
        night_results = self._announce_night_results()
        
        return {
            "phase": GamePhase.DAY_DISCUSSION,
            "night_results": night_results,
            "discussion_deadline": self.discussion_deadline,
            "alive_players": self.game_state.get_alive_player_ids()
        }
    
    def _announce_night_results(self) -> Dict[str, any]:
        """公布夜晚结果"""
        results = {
            "deaths": [],
            "saved_players": [],
            "protected_players": [],
            "special_events": []
        }
        
        if self.game_state.current_round_info:
            # 夜晚死亡
            for player_id in self.game_state.current_round_info.night_deaths:
                player = self.game_state.players[player_id]
                results["deaths"].append({
                    "player_id": player_id,
                    "player_name": player.name,
                    "role": player.role,
                    "cause": "werewolf_kill"
                })
            
            # 特殊事件（女巫救人、毒人等）
            for action in self.game_state.current_round_info.actions:
                if action.action_type == "witch_save":
                    results["saved_players"].append(action.target_id)
                elif action.action_type == "witch_poison":
                    if action.target_id in self.game_state.players:
                        target = self.game_state.players[action.target_id]
                        results["deaths"].append({
                            "player_id": action.target_id,
                            "player_name": target.name,
                            "role": target.role,
                            "cause": "witch_poison"
                        })
        
        return results
    
    def add_speech(self, player_id: int, speech_content: str) -> bool:
        """添加玩家发言"""
        if self.game_state.current_phase != GamePhase.DAY_DISCUSSION:
            return False
        
        player = self.game_state.players.get(player_id)
        if not player or not player.is_alive():
            return False
        
        # 检查是否超时
        if self.discussion_deadline and datetime.now() > self.discussion_deadline:
            return False
        
        speech = {
            "player_id": player_id,
            "player_name": player.name,
            "content": speech_content,
            "timestamp": datetime.now().isoformat(),
            "round": self.game_state.current_round
        }
        
        self.discussion_speeches.append(speech)
        
        # 记录到游戏历史
        if self.game_state.current_round_info:
            action = PlayerAction(
                player_id=player_id,
                action_type="speech",
                additional_data={"content": speech_content}
            )
            self.game_state.current_round_info.add_action(action)
        
        return True
    
    def get_discussion_summary(self) -> Dict[str, any]:
        """获取讨论阶段总结"""
        return {
            "total_speeches": len(self.discussion_speeches),
            "speeches": self.discussion_speeches,
            "active_players": len(set(speech["player_id"] for speech in self.discussion_speeches)),
            "discussion_time_remaining": self._get_time_remaining(self.discussion_deadline)
        }
    
    def start_voting_phase(self) -> Dict[str, any]:
        """开始投票阶段"""
        self.game_state.current_phase = GamePhase.DAY_VOTING
        self.game_state.clear_votes()
        
        # 设置投票时间限制
        if self.game_state.config.voting_time_limit > 0:
            self.voting_deadline = datetime.now() + timedelta(
                seconds=self.game_state.config.voting_time_limit
            )
        
        return {
            "phase": GamePhase.DAY_VOTING,
            "voting_deadline": self.voting_deadline,
            "eligible_voters": self.game_state.get_alive_player_ids(),
            "vote_targets": self.game_state.get_alive_player_ids()
        }
    
    def cast_vote(self, voter_id: int, target_id: Optional[int], reason: str = "") -> Dict[str, any]:
        """投票"""
        if self.game_state.current_phase != GamePhase.DAY_VOTING:
            return {"success": False, "error": "不在投票阶段"}
        
        # 检查投票者
        voter = self.game_state.players.get(voter_id)
        if not voter or not voter.is_alive():
            return {"success": False, "error": "投票者无效"}
        
        # 检查是否超时
        if self.voting_deadline and datetime.now() > self.voting_deadline:
            return {"success": False, "error": "投票时间已结束"}
        
        # 检查目标（允许弃权）
        if target_id is not None:
            target = self.game_state.players.get(target_id)
            if not target or not target.is_alive():
                return {"success": False, "error": "投票目标无效"}
            
            if target_id == voter_id:
                return {"success": False, "error": "不能投票给自己"}
        
        # 处理投票
        if target_id is not None:
            self.game_state.add_vote(voter_id, target_id, VoteType.ELIMINATION)
        else:
            # 弃权：移除之前的投票
            if voter_id in self.game_state.current_votes:
                old_target = self.game_state.current_votes[voter_id]
                del self.game_state.current_votes[voter_id]
                self.game_state.vote_counts[old_target] = max(0, 
                    self.game_state.vote_counts.get(old_target, 0) - 1)
        
        # 记录投票原因
        if self.game_state.current_round_info:
            vote = PlayerVote(
                voter_id=voter_id,
                target_id=target_id or 0,  # 0表示弃权
                vote_type=VoteType.ELIMINATION.name,
                round_number=self.game_state.current_round,
                reason=reason
            )
            self.game_state.current_round_info.add_vote(vote)
        
        return {
            "success": True,
            "voter_name": voter.name,
            "target_name": self.game_state.players[target_id].name if target_id else "弃权",
            "current_votes": dict(self.game_state.vote_counts)
        }
    
    def get_voting_summary(self) -> Dict[str, any]:
        """获取投票总结"""
        alive_players = self.game_state.get_alive_player_ids()
        voted_players = set(self.game_state.current_votes.keys())
        not_voted = set(alive_players) - voted_players
        
        # 按得票数排序
        vote_ranking = sorted(
            self.game_state.vote_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return {
            "total_voters": len(alive_players),
            "voted_count": len(voted_players),
            "not_voted": list(not_voted),
            "vote_counts": dict(self.game_state.vote_counts),
            "vote_ranking": vote_ranking,
            "voting_time_remaining": self._get_time_remaining(self.voting_deadline)
        }
    
    def resolve_voting(self) -> Dict[str, any]:
        """解决投票结果"""
        if not self.game_state.vote_counts:
            return {
                "result": "no_votes",
                "eliminated_player": None,
                "message": "没有人投票，无人被淘汰"
            }
        
        max_votes = max(self.game_state.vote_counts.values())
        candidates = [player_id for player_id, votes in self.game_state.vote_counts.items() 
                     if votes == max_votes]
        
        if len(candidates) == 1:
            # 单一候选人
            eliminated_id = candidates[0]
            return self._eliminate_player(eliminated_id, "majority_vote")
        
        elif len(candidates) > 1:
            # 平票处理
            return self._handle_tie_vote(candidates, max_votes)
        
        return {
            "result": "error",
            "eliminated_player": None,
            "message": "投票解决出错"
        }
    
    def _eliminate_player(self, player_id: int, reason: str) -> Dict[str, any]:
        """淘汰玩家"""
        player = self.game_state.players[player_id]
        self.game_state.eliminate_player(player_id)
        
        # 检查是否触发特殊角色技能
        special_events = []
        if player.role == Role.HUNTER:
            special_events.append({
                "type": "hunter_revenge",
                "player_id": player_id,
                "message": f"猎人 {player.name} 可以开枪带走一人"
            })
        
        return {
            "result": "eliminated",
            "eliminated_player": {
                "id": player_id,
                "name": player.name,
                "role": player.role
            },
            "reason": reason,
            "votes": self.game_state.vote_counts.get(player_id, 0),
            "special_events": special_events
        }
    
    def _handle_tie_vote(self, candidates: List[int], votes: int) -> Dict[str, any]:
        """处理平票"""
        if self.game_state.config.allow_tie_votes:
            # 允许平票：随机选择一个
            eliminated_id = random.choice(candidates)
            candidate_names = [self.game_state.players[pid].name for pid in candidates]
            
            result = self._eliminate_player(eliminated_id, "tie_vote_random")
            result["tie_candidates"] = candidate_names
            result["message"] = f"平票随机淘汰：{result['eliminated_player']['name']}"
            
            return result
        else:
            # 不允许平票：无人淘汰
            candidate_names = [self.game_state.players[pid].name for pid in candidates]
            
            return {
                "result": "tie_no_elimination",
                "eliminated_player": None,
                "tie_candidates": candidate_names,
                "votes": votes,
                "message": f"平票无人淘汰：{', '.join(candidate_names)} 各得 {votes} 票"
            }
    
    def handle_hunter_revenge(self, hunter_id: int, target_id: int) -> Dict[str, any]:
        """处理猎人复仇"""
        hunter = self.game_state.players.get(hunter_id)
        target = self.game_state.players.get(target_id)
        
        if not hunter or hunter.role != Role.HUNTER or hunter.is_alive():
            return {"success": False, "error": "猎人状态无效"}
        
        if not target or not target.is_alive():
            return {"success": False, "error": "目标无效"}
        
        if target_id == hunter_id:
            return {"success": False, "error": "猎人不能射击自己"}
        
        # 执行射击
        self.game_state.eliminate_player(target_id)
        
        # 记录行动
        if self.game_state.current_round_info:
            action = PlayerAction(
                player_id=hunter_id,
                action_type="hunter_shoot",
                target_id=target_id
            )
            self.game_state.current_round_info.add_action(action)
        
        return {
            "success": True,
            "hunter_name": hunter.name,
            "target_name": target.name,
            "target_role": target.role,
            "message": f"猎人 {hunter.name} 射击了 {target.name}"
        }
    
    def _get_time_remaining(self, deadline: Optional[datetime]) -> Optional[int]:
        """获取剩余时间（秒）"""
        if not deadline:
            return None
        
        remaining = (deadline - datetime.now()).total_seconds()
        return max(0, int(remaining))
    
    def is_phase_complete(self) -> bool:
        """检查阶段是否完成"""
        if self.game_state.current_phase == GamePhase.DAY_DISCUSSION:
            # 讨论阶段：检查是否超时
            if self.discussion_deadline:
                return datetime.now() > self.discussion_deadline
            return False
        
        elif self.game_state.current_phase == GamePhase.DAY_VOTING:
            # 投票阶段：检查是否所有人都投票或超时
            alive_players = set(self.game_state.get_alive_player_ids())
            voted_players = set(self.game_state.current_votes.keys())
            
            all_voted = alive_players <= voted_players
            time_up = self.voting_deadline and datetime.now() > self.voting_deadline
            
            return all_voted or time_up
        
        return False
