"""
控制台用户界面
提供简单的文本界面用于测试游戏功能
"""
import os
import time
from typing import Dict, List, Optional

from ..models.enums import GamePhase, GameResult, Role, VoteType
from ..models.game_state import GameState
from ..engine.game_engine import GameEngine
from ..players.ai_player import AIPlayer
from ..players.base_player import HumanPlayer


class ConsoleUI:
    """控制台用户界面"""
    
    def __init__(self):
        self.game_engine: Optional[GameEngine] = None
        self.players: Dict[int, AIPlayer] = {}
        self.human_player: Optional[HumanPlayer] = None
        
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_separator(self, char='-', length=50):
        """打印分隔线"""
        print(char * length)
    
    def print_header(self, title: str):
        """打印标题"""
        self.print_separator('=')
        print(f" {title} ".center(50, '='))
        self.print_separator('=')
    
    def display_game_state(self, game_state: GameState):
        """显示游戏状态"""
        self.print_header("游戏状态")
        
        print(f"游戏ID: {game_state.game_id}")
        print(f"当前阶段: {self._phase_to_chinese(game_state.current_phase)}")
        print(f"回合数: {game_state.current_round}")
        print(f"游戏结果: {self._result_to_chinese(game_state.game_result)}")
        
        self.print_separator()
        print("玩家状态:")
        
        for player_id, player in game_state.players.items():
            status = "存活" if player.is_alive() else "死亡"
            role_display = self._role_to_chinese(player.role)
            faction_display = self._faction_to_chinese(player.faction)
            
            print(f"  {player_id}. {player.name} - {role_display} ({faction_display}) - {status}")
        
        # 显示投票情况
        if game_state.current_votes:
            self.print_separator()
            print("当前投票:")
            for voter_id, target_id in game_state.current_votes.items():
                voter_name = game_state.players[voter_id].name
                target_name = game_state.players[target_id].name
                print(f"  {voter_name} -> {target_name}")
            
            print("得票统计:")
            for target_id, votes in game_state.vote_counts.items():
                target_name = game_state.players[target_id].name
                print(f"  {target_name}: {votes}票")
    
    def _phase_to_chinese(self, phase: GamePhase) -> str:
        """阶段转中文"""
        phase_map = {
            GamePhase.SETUP: "游戏设置",
            GamePhase.NIGHT: "夜晚",
            GamePhase.DAY_DISCUSSION: "白天讨论",
            GamePhase.DAY_VOTING: "白天投票",
            GamePhase.GAME_OVER: "游戏结束"
        }
        return phase_map.get(phase, str(phase))
    
    def _result_to_chinese(self, result: GameResult) -> str:
        """结果转中文"""
        result_map = {
            GameResult.ONGOING: "进行中",
            GameResult.VILLAGERS_WIN: "村民胜利",
            GameResult.WEREWOLVES_WIN: "狼人胜利"
        }
        return result_map.get(result, str(result))
    
    def _role_to_chinese(self, role: Role) -> str:
        """角色转中文"""
        role_map = {
            Role.VILLAGER: "村民",
            Role.WEREWOLF: "狼人",
            Role.SEER: "预言家",
            Role.WITCH: "女巫",
            Role.HUNTER: "猎人",
            Role.GUARD: "守卫",
            Role.FOOL: "白痴"
        }
        return role_map.get(role, str(role))
    
    def _faction_to_chinese(self, faction) -> str:
        """阵营转中文"""
        from ..models.enums import Faction
        faction_map = {
            Faction.VILLAGERS: "村民阵营",
            Faction.WEREWOLVES: "狼人阵营",
            Faction.NEUTRAL: "中立阵营"
        }
        return faction_map.get(faction, str(faction))
    
    def get_user_input(self, prompt: str, valid_options: Optional[List[str]] = None) -> str:
        """获取用户输入"""
        while True:
            user_input = input(f"{prompt}: ").strip()
            
            if valid_options is None:
                return user_input
            
            if user_input in valid_options:
                return user_input
            
            print(f"无效输入，请选择: {', '.join(valid_options)}")
    
    def get_player_choice(self, game_state: GameState, prompt: str) -> Optional[int]:
        """获取玩家选择"""
        alive_players = game_state.get_alive_players()
        
        print(f"\n{prompt}")
        print("可选玩家:")
        for player in alive_players:
            print(f"  {player.player_id}. {player.name}")
        
        print("  0. 弃权/跳过")
        
        while True:
            try:
                choice = int(input("请选择玩家ID: "))
                if choice == 0:
                    return None
                
                if any(p.player_id == choice for p in alive_players):
                    return choice
                
                print("无效的玩家ID，请重新选择")
            except ValueError:
                print("请输入有效的数字")
    
    def display_round_summary(self, game_state: GameState):
        """显示回合总结"""
        if not game_state.rounds_history:
            return
        
        last_round = game_state.rounds_history[-1]
        
        self.print_header(f"第{last_round.round_number}回合总结")
        
        if last_round.eliminated_player_id:
            eliminated = game_state.players[last_round.eliminated_player_id]
            print(f"被淘汰: {eliminated.name} ({self._role_to_chinese(eliminated.role)})")
        
        if last_round.night_deaths:
            print("夜晚死亡:")
            for player_id in last_round.night_deaths:
                player = game_state.players[player_id]
                print(f"  {player.name} ({self._role_to_chinese(player.role)})")
        
        if last_round.actions:
            print("特殊行动:")
            for action in last_round.actions:
                actor = game_state.players[action.player_id]
                action_desc = self._action_to_chinese(action.action_type)
                target_desc = ""
                if action.target_id:
                    target = game_state.players[action.target_id]
                    target_desc = f" -> {target.name}"
                print(f"  {actor.name}: {action_desc}{target_desc}")
    
    def _action_to_chinese(self, action_type: str) -> str:
        """行动类型转中文"""
        action_map = {
            "seer_check": "预言家查验",
            "guard_protect": "守卫保护",
            "witch_save": "女巫救人",
            "witch_poison": "女巫毒人",
            "werewolf_kill": "狼人杀人"
        }
        return action_map.get(action_type, action_type)
    
    def wait_for_continue(self):
        """等待用户继续"""
        input("\n按回车键继续...")
    
    def display_game_result(self, game_state: GameState):
        """显示游戏结果"""
        self.print_header("游戏结束")
        
        result_text = self._result_to_chinese(game_state.game_result)
        print(f"游戏结果: {result_text}")
        
        print("\n最终玩家状态:")
        for player_id, player in game_state.players.items():
            status = "存活" if player.is_alive() else "死亡"
            role_display = self._role_to_chinese(player.role)
            print(f"  {player.name} - {role_display} - {status}")
        
        print(f"\n游戏进行了 {game_state.current_round} 回合")
    
    def show_menu(self) -> str:
        """显示主菜单"""
        self.print_header("狼人杀AI游戏")
        
        print("1. 开始新游戏")
        print("2. 游戏规则")
        print("3. 退出")
        
        return self.get_user_input("请选择", ["1", "2", "3"])
    
    def show_game_rules(self):
        """显示游戏规则"""
        self.print_header("游戏规则")
        
        rules = """
        狼人杀游戏规则：
        
        1. 角色介绍：
           - 村民：普通村民，白天参与讨论和投票
           - 狼人：夜晚杀人，白天伪装成村民
           - 预言家：每晚可以查验一个人的身份
           - 女巫：有一瓶解药和一瓶毒药，各用一次
           - 守卫：每晚可以保护一个人不被狼人杀死
           - 猎人：被淘汰时可以开枪带走一个人
        
        2. 游戏流程：
           - 夜晚：狼人杀人，特殊角色使用技能
           - 白天：讨论阶段，所有人发言讨论
           - 投票：投票淘汰一个人
        
        3. 胜利条件：
           - 村民阵营：淘汰所有狼人
           - 狼人阵营：狼人数量 >= 村民数量
        """
        
        print(rules)
        self.wait_for_continue()
    
    def run_demo(self):
        """运行演示"""
        while True:
            self.clear_screen()
            choice = self.show_menu()
            
            if choice == "1":
                self.start_new_game()
            elif choice == "2":
                self.show_game_rules()
            elif choice == "3":
                print("感谢游戏！")
                break
    
    def start_new_game(self):
        """开始新游戏"""
        self.clear_screen()
        self.print_header("创建新游戏")
        
        # 简化配置：固定6人局
        from ..models.game_state import GameConfig
        from ..models.enums import Role
        
        config = GameConfig(
            total_players=6,
            role_distribution={
                Role.VILLAGER: 2,
                Role.WEREWOLF: 2,
                Role.SEER: 1,
                Role.WITCH: 1
            }
        )
        
        # 创建游戏引擎
        self.game_engine = GameEngine(config)
        
        # 创建玩家
        player_names = [f"AI玩家{i+1}" for i in range(6)]
        
        # 创建游戏
        game_state = self.game_engine.create_game("demo_game", player_names)
        
        # 创建AI玩家实例
        self.players = {}
        for player_id, player_info in game_state.players.items():
            ai_player = AIPlayer(player_id, player_info.name)
            ai_player.set_role(player_info.role)
            self.players[player_id] = ai_player
        
        print("游戏创建成功！")
        self.display_game_state(game_state)
        self.wait_for_continue()
        
        # 开始游戏循环
        self.game_loop()
    
    def game_loop(self):
        """游戏主循环"""
        if not self.game_engine:
            return
        
        self.game_engine.start_game()
        
        while not self.game_engine.is_game_over():
            game_state = self.game_engine.get_game_state()
            
            self.clear_screen()
            self.display_game_state(game_state)
            
            if game_state.current_phase == GamePhase.NIGHT:
                self.handle_night_phase(game_state)
            elif game_state.current_phase == GamePhase.DAY_DISCUSSION:
                self.handle_day_discussion(game_state)
            elif game_state.current_phase == GamePhase.DAY_VOTING:
                self.handle_day_voting(game_state)
            
            self.game_engine.advance_phase()
            
            if game_state.current_phase != GamePhase.GAME_OVER:
                self.display_round_summary(game_state)
                self.wait_for_continue()
        
        # 游戏结束
        final_state = self.game_engine.get_game_state()
        self.clear_screen()
        self.display_game_result(final_state)
        self.wait_for_continue()
    
    def handle_night_phase(self, game_state: GameState):
        """处理夜晚阶段"""
        print("\n=== 夜晚阶段 ===")
        print("AI玩家正在进行夜晚行动...")
        
        # 模拟AI玩家行动
        time.sleep(2)  # 模拟思考时间
        
        # 狼人杀人
        werewolves = game_state.get_werewolves()
        if werewolves:
            # 简化：第一个狼人代表狼人团队投票
            werewolf = werewolves[0]
            ai_werewolf = self.players[werewolf.player_id]
            target = ai_werewolf.make_vote_decision(game_state, VoteType.WEREWOLF_KILL)
            if target:
                self.game_engine.process_vote(werewolf.player_id, target, VoteType.WEREWOLF_KILL)
        
        # 处理特殊角色行动
        for player_id, player_info in game_state.players.items():
            if not player_info.is_alive():
                continue
                
            ai_player = self.players[player_id]
            
            if player_info.role == Role.SEER:
                target = ai_player.make_special_action_decision(game_state, "seer_check")
                if target:
                    self.game_engine.process_special_action(player_id, "seer_check", target)
            
            elif player_info.role == Role.GUARD:
                target = ai_player.make_special_action_decision(game_state, "guard_protect")
                if target:
                    self.game_engine.process_special_action(player_id, "guard_protect", target)
        
        # 处理狼人杀人结果
        self.game_engine.process_werewolf_kill()
    
    def handle_day_discussion(self, game_state: GameState):
        """处理白天讨论阶段"""
        print("\n=== 白天讨论阶段 ===")
        
        # 显示夜晚结果
        if game_state.current_round_info and game_state.current_round_info.night_deaths:
            print("昨晚死亡的玩家:")
            for player_id in game_state.current_round_info.night_deaths:
                player = game_state.players[player_id]
                print(f"  {player.name} ({self._role_to_chinese(player.role)})")
        
        # AI玩家发言
        print("\n玩家发言:")
        for player_id, player_info in game_state.players.items():
            if player_info.is_alive():
                ai_player = self.players[player_id]
                speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
                print(f"  {player_info.name}: {speech}")
        
        time.sleep(3)  # 模拟讨论时间
    
    def handle_day_voting(self, game_state: GameState):
        """处理白天投票阶段"""
        print("\n=== 白天投票阶段 ===")
        
        # AI玩家投票
        for player_id, player_info in game_state.players.items():
            if player_info.is_alive():
                ai_player = self.players[player_id]
                target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
                if target:
                    self.game_engine.process_vote(player_id, target, VoteType.ELIMINATION)
                    target_name = game_state.players[target].name
                    print(f"  {player_info.name} 投票给 {target_name}")
        
        time.sleep(2)  # 模拟投票时间
