"""
游戏枚举定义
定义游戏中使用的各种枚举类型
"""
from enum import Enum, auto


class Role(Enum):
    """角色枚举"""
    VILLAGER = auto()      # 村民
    WEREWOLF = auto()      # 狼人
    SEER = auto()          # 预言家
    WITCH = auto()         # 女巫
    HUNTER = auto()        # 猎人
    GUARD = auto()         # 守卫
    FOOL = auto()          # 白痴


class GamePhase(Enum):
    """游戏阶段枚举"""
    SETUP = auto()         # 游戏设置阶段
    NIGHT = auto()         # 夜晚阶段
    DAY_DISCUSSION = auto() # 白天讨论阶段
    DAY_VOTING = auto()    # 白天投票阶段
    GAME_OVER = auto()     # 游戏结束


class PlayerStatus(Enum):
    """玩家状态枚举"""
    ALIVE = auto()         # 存活
    DEAD = auto()          # 死亡
    PROTECTED = auto()     # 被保护


class VoteType(Enum):
    """投票类型枚举"""
    ELIMINATION = auto()   # 处决投票
    WEREWOLF_KILL = auto() # 狼人杀人投票


class ActionType(Enum):
    """行动类型枚举"""
    VOTE = auto()          # 投票
    WEREWOLF_KILL = auto() # 狼人杀人
    SEER_CHECK = auto()    # 预言家查验
    WITCH_SAVE = auto()    # 女巫救人
    WITCH_POISON = auto()  # 女巫毒人
    HUNTER_SHOOT = auto()  # 猎人开枪
    GUARD_PROTECT = auto() # 守卫保护


class GameResult(Enum):
    """游戏结果枚举"""
    VILLAGERS_WIN = auto() # 村民胜利
    WEREWOLVES_WIN = auto() # 狼人胜利
    ONGOING = auto()       # 游戏进行中


class Faction(Enum):
    """阵营枚举"""
    VILLAGERS = auto()     # 村民阵营
    WEREWOLVES = auto()    # 狼人阵营
    NEUTRAL = auto()       # 中立阵营
