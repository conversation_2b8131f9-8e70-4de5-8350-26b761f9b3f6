"""
玩家相关数据结构
定义玩家的基本信息和状态
"""
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from .enums import Role, PlayerStatus, Faction


@dataclass
class PlayerInfo:
    """玩家基本信息"""
    player_id: int
    name: str
    role: Role
    status: PlayerStatus = PlayerStatus.ALIVE
    faction: Optional[Faction] = None
    
    def __post_init__(self):
        """初始化后设置阵营"""
        if self.faction is None:
            self.faction = self._get_faction_by_role()
    
    def _get_faction_by_role(self) -> Faction:
        """根据角色确定阵营"""
        if self.role == Role.WEREWOLF:
            return Faction.WEREWOLVES
        elif self.role in [Role.VILLAGER, Role.SEER, Role.WITCH, Role.HUNTER, Role.GUARD]:
            return Faction.VILLAGERS
        else:
            return Faction.NEUTRAL
    
    def is_alive(self) -> bool:
        """检查玩家是否存活"""
        return self.status == PlayerStatus.ALIVE
    
    def is_werewolf(self) -> bool:
        """检查是否为狼人"""
        return self.role == Role.WEREWOLF
    
    def is_villager_faction(self) -> bool:
        """检查是否为村民阵营"""
        return self.faction == Faction.VILLAGERS


@dataclass
class PlayerAction:
    """玩家行动记录"""
    player_id: int
    action_type: str
    target_id: Optional[int] = None
    round_number: int = 0
    phase: str = ""
    timestamp: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PlayerVote:
    """玩家投票记录"""
    voter_id: int
    target_id: int
    vote_type: str
    round_number: int
    confidence: float = 1.0  # 投票信心度 (0-1)
    reason: str = ""  # 投票理由


@dataclass
class PlayerKnowledge:
    """玩家知识状态（AI使用）"""
    player_id: int
    known_roles: Dict[int, Role] = field(default_factory=dict)  # 已知的其他玩家角色
    suspected_roles: Dict[int, Dict[Role, float]] = field(default_factory=dict)  # 怀疑的角色概率
    trust_levels: Dict[int, float] = field(default_factory=dict)  # 对其他玩家的信任度
    alliance_members: List[int] = field(default_factory=list)  # 盟友列表
    enemy_list: List[int] = field(default_factory=list)  # 敌人列表
    
    def update_role_knowledge(self, target_id: int, role: Role, certainty: float = 1.0):
        """更新角色知识"""
        if certainty >= 0.9:  # 高确定性直接记录为已知
            self.known_roles[target_id] = role
        else:
            if target_id not in self.suspected_roles:
                self.suspected_roles[target_id] = {}
            self.suspected_roles[target_id][role] = certainty
    
    def get_most_likely_role(self, target_id: int) -> Optional[Role]:
        """获取最可能的角色"""
        if target_id in self.known_roles:
            return self.known_roles[target_id]
        
        if target_id in self.suspected_roles:
            suspicions = self.suspected_roles[target_id]
            if suspicions:
                return max(suspicions.items(), key=lambda x: x[1])[0]
        
        return None
