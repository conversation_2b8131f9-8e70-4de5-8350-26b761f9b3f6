#!/usr/bin/env python3
"""
基础功能测试
验证Phase 1实现的核心功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase, GameResult
from src.models.game_state import GameConfig, GameState
from src.models.player import PlayerInfo
from src.engine.game_engine import GameEngine
from src.players.ai_player import AIPlayer


def test_game_config():
    """测试游戏配置"""
    print("=== 测试游戏配置 ===")
    
    config = GameConfig(
        total_players=6,
        role_distribution={
            Role.VILLAGER: 2,
            Role.WEREWOLF: 2,
            Role.SEER: 1,
            Role.WITCH: 1
        }
    )
    
    print(f"总玩家数: {config.total_players}")
    print(f"角色分配: {config.role_distribution}")
    print(f"配置有效: {config.validate()}")
    print()


def test_player_creation():
    """测试玩家创建"""
    print("=== 测试玩家创建 ===")
    
    # 创建不同角色的玩家
    villager = PlayerInfo(1, "村民1", Role.VILLAGER)
    werewolf = PlayerInfo(2, "狼人1", Role.WEREWOLF)
    seer = PlayerInfo(3, "预言家", Role.SEER)
    
    players = [villager, werewolf, seer]
    
    for player in players:
        print(f"玩家: {player.name}")
        print(f"  角色: {player.role}")
        print(f"  阵营: {player.faction}")
        print(f"  存活: {player.is_alive()}")
        print(f"  是狼人: {player.is_werewolf()}")
        print(f"  是村民阵营: {player.is_villager_faction()}")
        print()


def test_game_engine():
    """测试游戏引擎"""
    print("=== 测试游戏引擎 ===")
    
    # 创建配置
    config = GameConfig(
        total_players=4,
        role_distribution={
            Role.VILLAGER: 1,
            Role.WEREWOLF: 2,
            Role.SEER: 1
        }
    )
    
    # 创建游戏引擎
    engine = GameEngine(config)
    
    # 创建游戏
    player_names = ["Alice", "Bob", "Charlie", "David"]
    game_state = engine.create_game("test_game", player_names)
    
    print(f"游戏ID: {game_state.game_id}")
    print(f"当前阶段: {game_state.current_phase}")
    print(f"玩家数量: {len(game_state.players)}")
    
    print("玩家列表:")
    for player_id, player in game_state.players.items():
        print(f"  {player_id}. {player.name} - {player.role} ({player.faction})")
    
    # 开始游戏
    engine.start_game()
    print(f"游戏开始后阶段: {game_state.current_phase}")
    print(f"当前回合: {game_state.current_round}")
    print()


def test_ai_player():
    """测试AI玩家"""
    print("=== 测试AI玩家 ===")
    
    # 创建AI玩家
    ai_player = AIPlayer(1, "AI测试玩家")
    ai_player.set_role(Role.VILLAGER)
    
    print(f"AI玩家: {ai_player.name}")
    print(f"角色: {ai_player.role}")
    print(f"个性特征: {ai_player.personality_traits}")
    
    # 创建简单的游戏状态用于测试
    config = GameConfig(
        total_players=3,
        role_distribution={
            Role.VILLAGER: 1,
            Role.WEREWOLF: 1,
            Role.SEER: 1
        }
    )
    
    players = {
        1: PlayerInfo(1, "AI玩家", Role.VILLAGER),
        2: PlayerInfo(2, "狼人", Role.WEREWOLF),
        3: PlayerInfo(3, "预言家", Role.SEER)
    }
    
    game_state = GameState(
        game_id="test",
        config=config,
        players=players,
        current_phase=GamePhase.DAY_VOTING
    )
    
    # 测试AI决策
    from src.models.enums import VoteType
    vote_target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
    print(f"AI投票目标: {vote_target}")
    
    speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
    print(f"AI发言: {speech}")
    print()


def test_game_flow():
    """测试游戏流程"""
    print("=== 测试游戏流程 ===")
    
    # 创建配置
    config = GameConfig(
        total_players=4,
        role_distribution={
            Role.VILLAGER: 1,
            Role.WEREWOLF: 2,
            Role.SEER: 1
        }
    )
    
    # 创建游戏引擎
    engine = GameEngine(config)
    
    # 事件处理器
    def on_game_started(game_state):
        print(f"游戏开始事件: 当前阶段 {game_state.current_phase}")
    
    def on_phase_changed(game_state, new_phase):
        print(f"阶段变更事件: {new_phase}")
    
    def on_player_eliminated(game_state, player_id):
        player = game_state.players[player_id]
        print(f"玩家淘汰事件: {player.name} ({player.role})")
    
    # 注册事件处理器
    engine.register_event_handler("game_started", on_game_started)
    engine.register_event_handler("phase_changed", on_phase_changed)
    engine.register_event_handler("player_eliminated", on_player_eliminated)
    
    # 创建游戏
    player_names = ["Alice", "Bob", "Charlie", "David"]
    game_state = engine.create_game("flow_test", player_names)
    
    # 开始游戏
    engine.start_game()
    
    # 模拟几个阶段转换
    print("模拟阶段转换:")
    for i in range(3):
        print(f"  转换 {i+1}: {game_state.current_phase}")
        engine.advance_phase()
        if engine.is_game_over():
            break
    
    print(f"最终游戏结果: {game_state.game_result}")
    print()


def main():
    """主测试函数"""
    print("狼人杀AI游戏 - Phase 1 基础功能测试")
    print("=" * 50)
    
    try:
        test_game_config()
        test_player_creation()
        test_game_engine()
        test_ai_player()
        test_game_flow()
        
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
